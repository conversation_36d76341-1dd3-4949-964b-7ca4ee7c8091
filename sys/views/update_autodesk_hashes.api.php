<?php
/**
 * API endpoint for updating Autodesk catalog hashes
 */
namespace api\admin;

use data_importer;

use system\users;

// Check for API authentication and proper permissions
// First check if user is authenticated
$current_user = users::checkAuth();
if (!$current_user) {
    return api_error('Unauthorized - Not authenticated', 401);
}

// Then check if user has admin or dev role
$user_role = $current_user['role'];
if ($user_role !== 'admin' && $user_role !== 'dev') {
    return api_error('Forbidden - Insufficient privileges', 403);
}

/**
 * Update Autodesk catalog hashes
 *
 * @param array $p Parameters (optional)
 * @return array API response
 */
function update_autodesk_hashes($p = []) {
    // Include necessary files
    require_once(DOC_ROOT . '/resources/classes/data_importer.class.php');

    // Set batch size from parameters or use default
    $batch_size = isset($p['batch_size']) ? (int)$p['batch_size'] : 100;

    // Run the update process
    $result = DataImporter::update_autodesk_catalog_hashes($batch_size);

    // Return the result
    if (isset($result['error'])) {
        return [
            'status' => 'error',
            'message' => $result['error'],
            'data' => $result
        ];
    } else {
        return [
            'status' => 'success',
            'message' => 'Hash update completed successfully',
            'data' => $result
        ];
    }
}
