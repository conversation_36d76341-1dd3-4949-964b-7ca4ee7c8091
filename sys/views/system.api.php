<?php
namespace api\system;
use autodesk_api\autodesk_api;
use autodesk_api\autodesk_subscription;




print_rr($valid_functions, $action);
// Check if the function exists and is valid


function update_products(){
    print_rr('updating products');
    $autodesk = new autodesk_api();
    $response = $autodesk->products->get_catalog();
    print_rr($response);
    return '<pre>' . json_encode($response, JSON_PRETTY_PRINT) . '</pre>';
}

function update_prices(){
    $autodesk = new autodesk_api();
    $autodesk->products->database_update_autodesk_pricing();
        
    }
//function subscriptions_get_from_api(){
//    print_rr('updating subscriptions','starting..',false);
//    $api = new autodesk_api();
//    print_rr($api->subscriptions->get_from_api(),'.. completed: ',false);
//}


function subscriptions_get_from_api(){
    print_rr('updating subscriptions','starting..',false);
    autodesk_api::$log_target = "subscription_import";
    tcs_log('Starting subscription update
    ***********************************************************************************************************************************************************',"subscription_import");
    $download = false;
    $decrypt = false;
    $enc_file_path = FS_DOC_ROOT . 'feeds/subscriptions.csv.zip.enc';
    $csv_file_path = FS_DOC_ROOT . 'feeds/subscriptions.csv';
    tcs_log("Looking for file: " . $enc_file_path,"subscription_import");
    if (file_exists($enc_file_path)) {
        $file_age = time() - filemtime($enc_file_path);
        tcs_log("file found: " . $enc_file_path . " - age: " . $file_age,"subscription_import");
        if ($file_age > 8640000) {
            $decrypt = true;
            tcs_log("$file_age > 8640000, decrypting" ,"subscription_import");
        }else{
            tcs_log("using cached enc file" ,"subscription_import");
            
        }
    } else {
        tcs_log("file not found: " . $enc_file_path . " - age: " . $file_age,"subscription_import");
        $download = true;
    }
    tcs_log("Looking for file: " . $csv_file_path,"subscription_import");
    if (file_exists($csv_file_path)) {
            $file_age = time() - filemtime($csv_file_path);
            tcs_log("file found: " . $csv_file_path . " - age: " . $file_age,"subscription_import");
            if ($file_age > 8640000) {
                tcs_log("$file_age > 8640000: " . $csv_file_path . " - age: " . $file_age,"subscription_import");
                $download = true;
                $decrypt = false;
            }else{
                tcs_log("using cached csv file without api call" ,"subscription_import");
            }
    }else{
          tcs_log("file not found: " . $csv_file_path . " - age: " . $file_age,"subscription_import");
    }

    $api = new autodesk_api();
    if ($download) {
        tcs_log("initiating api call", "subscription_import");
        return print_rr($api->subscriptions->get_from_api(),'.. completed: ',false);
    }
    if ($decrypt) {
        try {
            tcs_log("Getting decryption info", "subscription_import","subscription_import");
            $info = $api->subscriptions->database_get_api_export_data();
            autodesk_api::log_message("Decrypting file with: " . print_r($info, true), "subscription_import");
            //return ['status' => 'success', 'response' => print_r($info, true)];
            $decrypted_file = $api->auth->decryptFile($enc_file_path, $csv_file_path, $info->password, "subscription_import");

        } catch (Exception $e) {
            $response = $e->getMessage();
            return ['status' => 'fail', 'response' => $response];
        }
    }
    return print_rr(autodesk_api::import_csv_into_database(
        mapping: autodesk_subscription::$subscription_column_mapping,
        csv_file_path: $csv_file_path,
        debug: true));
}

function quotes_get_from_api(){
    print_rr('updating quotes','starting..',false);
    autodesk_api::$log_target = "quote_import";
    tcs_log('Starting quote update
    ***********************************************************************************************************************************************************',"subscription_import");
    $download = true;
    $decrypt = true;
    $enc_file_path = FS_DOC_ROOT . 'feeds/quotes.json.zip.enc';
    $json_file_path = FS_DOC_ROOT . 'feeds/quotes.json';
    tcs_log("Looking for file: " . $enc_file_path,"subscription_import");
    if (file_exists($enc_file_path)) {
        $file_age = time() - filemtime($enc_file_path);
        tcs_log("file found: " . $enc_file_path . " - age: " . $file_age,"subscription_import");
        if ($file_age > 8640000) {
            tcs_log("$file_age > 8640000, downloading" ,"subscription_import");
        }else{
            $download = false;
            tcs_log("using cached enc file" ,"subscription_import");
            
        }
    } else {
        tcs_log("file not found: " . $enc_file_path . " - age: " . $file_age,"subscription_import");
    }
    tcs_log("Looking for file: " . $json_file_path,"subscription_import");
    if (file_exists($json_file_path)) {
            $file_age = time() - filemtime($json_file_path);
            tcs_log("file found: " . $json_file_path . " - age: " . $file_age,"subscription_import");
            if ($file_age > 8640000) {
                tcs_log("$file_age > 8640000: " . $json_file_path . " - age: " . $file_age,"subscription_import");
                $download = false;
                $decrypt = false;
            }else{
                tcs_log("using cached json file" ,"subscription_import");
            }
    }else{         
        tcs_log("file not found: " . $json_file_path,"subscription_import");
    }

    $api = new autodesk_api();
    if ($download) {
        return tcs_log($api->quotes->get_from_api(),'subscription_import');
    }
    if ($decrypt) {
        try {
            tcs_log("Getting decryption info", "quote_import","subscription_import");
            $info = json_decode($api->quotes->database_get_api_export_data());
            autodesk_api::log_message("Decrypting file with: " . print_r($info, true), "quote_import");
            //return ['status' => 'success', 'response' => print_r($info, true)];
            $decrypted_file = $api->auth->decryptFile($enc_file_path, $json_file_path, $info->password, "quote_import");

        } catch (Exception $e) {
            $response = $e->getMessage();
            return ['status' => 'fail', 'response' => $response];
        }
    }
    return print_rr($api->quotes->import_autodesk_quotes_into_database($json_file_path),'.. completed: ',false);

}

function autodesk_send_raw_api_call($data) {
    $autodesk = new autodesk_api();
    print_rr($data);
    $json = preg_replace('/\R\s*/','',$data['json']);
     $json = mb_convert_encoding($json, 'UTF-8');
    $response[] = $autodesk->api->raw_call($data['requestType'],$data['endpoint'],$data['params'],$json);
    $response[] = $autodesk->api->debugLog;
    return '<pre>' . json_encode($response, JSON_PRETTY_PRINT) . '</pre>';
}
?>

