<?php
/**
 * Admin interface for updating Autodesk catalog hashes
 * This tool requires admin or dev privileges
 */

use system\users;
use data_importer\data_importer;

// Check if user has admin or dev privileges
// The requireRole method will handle redirecting to login if not authenticated
// and will exit with 'Access Denied' if the user doesn't have sufficient privileges
users::requireRole('admin');
// Process form submission
$result = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_hashes'])) {
    // Set batch size
    $batch_size = isset($_POST['batch_size']) ? (int)$_POST['batch_size'] : 100;

    // Run the update process
    $result = data_importer::update_autodesk_catalog_hashes($batch_size);
}
?>

<div class="container mt-4">
    <h1>Update Autodesk Catalog Hashes</h1>

    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title">About This Tool</h5>
            <p class="card-text">
                This tool updates the unique hashes in the <code>products_autodesk_catalog</code> table and related tables
                to ensure consistency between old and new implementations. It uses the existing <code>hash_string</code> values
                to generate new hashes with the current algorithm, maintaining database relationships.
            </p>
            <p class="card-text">
                The process will:
            </p>
            <ul>
                <li>Update all hashes in the <code>products_autodesk_catalog</code> table</li>
                <li>Update related tables that reference these hashes to maintain database integrity</li>
                <li>Process records in batches to avoid memory issues</li>
            </ul>
            <div class="alert alert-warning">
                <strong>Warning:</strong> This process may take some time depending on the number of records in the database.
                It's recommended to run this during off-peak hours.
            </div>
        </div>
    </div>

    <?php if ($result): ?>
        <div class="card mb-4">
            <div class="card-header">
                <?php if (isset($result['error'])): ?>
                    <h5 class="text-danger">Error</h5>
                <?php else: ?>
                    <h5 class="text-success">Success</h5>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if (isset($result['error'])): ?>
                    <p class="card-text text-danger"><?php echo $result['error']; ?></p>
                <?php else: ?>
                    <p class="card-text">Hash update completed successfully.</p>
                    <ul>
                        <li>Total records processed: <?php echo $result['total_records']; ?></li>
                        <li>Updated catalog records: <?php echo $result['updated_catalog_records']; ?></li>
                        <li>Updated relation records: <?php echo $result['updated_relation_records']; ?></li>
                        <li>Failed records: <?php echo $result['failed_records']; ?></li>
                    </ul>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header">
            <h5>Run Hash Update</h5>
        </div>
        <div class="card-body">
            <form method="post" action="">
                <div class="form-group mb-3">
                    <label for="batch_size">Batch Size:</label>
                    <input type="number" class="form-control" id="batch_size" name="batch_size" value="100" min="10" max="1000">
                    <small class="form-text text-muted">Number of records to process in each batch. Smaller batches use less memory but take longer.</small>
                </div>

                <button type="submit" name="update_hashes" class="btn btn-primary">Update Hashes</button>
            </form>
        </div>
    </div>
</div>
