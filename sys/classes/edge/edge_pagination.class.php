<?php
namespace edge;

class pagination {
    public int $index;       // Current index (0-based)
    public int $current_page_number;   // Current page (1-based)
    public int $items_per_page;   // Remaining pages
    public int $item_count;       // Total items in the array
    public int $first_item;       // Is this the first page
    public int $last_item;        // Is this the last page
    public bool $first;       // Is this the first page
    public bool $last;        // Is this the last page
    public int $total_items;        //
    public int $total_pages;         //

    public function __construct($item_count, $first_item = 0, $current_page_number = 1, $items_per_page = 100)
    {
        $this->index = 0;
        $this->$current_page_number = $current_page_number;
        $this->items_per_page = $items_per_page;
        $this->item_count = $item_count;

        $this->first_item = $first_item + 1;
// print_rr(i:$this->first_item . ' + ' . $this->items_per_page . ' = ' . ($this->first_item + $this->items_per_page - 1),fl:true);
        $this->last_item = $this->first_item + (min($this->items_per_page, $this->item_count) - 1);
        $this->total_items = $this->item_count;
        $this->total_pages = ceil($this->item_count / $this->items_per_page) - 1;
    }

    public function page_array(): array
    {
        $pages = [];
        for ($i = $this->current_page - 3; $i < ($this->current_page + 3); $i++) {
            if ($i > 0 && $i <= $this->total_pages) {
                $pages[] = $i;
            }
        }
        if ($this->total_pages <= 3) return $pages;
        $pages[] = "...";
        for ($i = $this->item_count - 3; $i <= $this->item_count; $i++) {
            if ($i > 0 && ($i <= $this->item_count)) {
                $pages[] = $i;
            }
        }
        return $pages;
    }
}

?>