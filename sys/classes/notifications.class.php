<?php
namespace system;

/**
 * Class for handling multiple notifications and notification preferences
 */
class notifications {
    private $db;
    private $user_id;

    /**
     * Constructor
     */
    public function __construct() {
        global $db;
        $this->db = $db;
        $this->user_id = users::checkAuth()['id'] ?? null;
    }

    /**
     * Create a new notification for a user
     *
     * @param int $user_id User ID to send notification to
     * @param string $type Notification type
     * @param string $title Notification title
     * @param string $message Notification message
     * @param string|null $link Optional link to include with notification
     * @return int|bool The notification ID if successful, false otherwise
     */
    public function create($user_id, $type, $title, $message, $link = null) {
        $notification = new notification();
        return $notification->create($user_id, $type, $title, $message, $link);
    }

    /**
     * Get notifications for the current user
     *
     * @param bool $unread_only Whether to get only unread notifications
     * @param int $limit Maximum number of notifications to return
     * @param int $offset Offset for pagination
     * @return array Notifications
     */
    public function getForCurrentUser($unread_only = false, $limit = 10, $offset = 0) {
        if (!$this->user_id) {
            return [];
        }

        $query = "SELECT * FROM autobooks_notifications
                 WHERE user_id = :user_id";

        if ($unread_only) {
            $query .= " AND is_read = 0";
        }

        $query .= " ORDER BY created_at DESC LIMIT :limit OFFSET :offset";

        $params = [
            ':user_id' => $this->user_id,
            ':limit' => $limit,
            ':offset' => $offset
        ];

        $result = tep_db_query($query, null, $params);

        $notifications = [];
        while ($row = tep_db_fetch_array($result)) {
            $notifications[] = $row;
        }

        return $notifications;
    }

    /**
     * Get the count of unread notifications for the current user
     *
     * @return int Count of unread notifications
     */
    public function getUnreadCount() {
        if (!$this->user_id) {
            return 0;
        }

        $query = "SELECT COUNT(*) as count FROM autobooks_notifications
                 WHERE user_id = :user_id AND is_read = 0";

        $params = [':user_id' => $this->user_id];

        $result = tep_db_query($query, null, $params);

        if ($row = tep_db_fetch_array($result)) {
            return (int)$row['count'];
        }

        return 0;
    }

    /**
     * Mark a notification as read
     *
     * @param int $notification_id Notification ID
     * @return bool Whether the operation was successful
     */
    public function markAsRead($notification_id) {
        if (!$this->user_id) {
            return false;
        }

        $notification = new notification($notification_id);
        return $notification->markAsRead($this->user_id);
    }

    /**
     * Mark all notifications as read for the current user
     *
     * @return bool Whether the operation was successful
     */
    public function markAllAsRead() {
        if (!$this->user_id) {
            return false;
        }

        $query = "UPDATE autobooks_notifications
                 SET is_read = 1, read_at = NOW()
                 WHERE user_id = :user_id AND is_read = 0";

        $params = [':user_id' => $this->user_id];

        $result = tep_db_query($query, null, $params);

        return $result !== false;
    }

    /**
     * Get notification preferences for the current user
     *
     * @return array Notification preferences
     */
    public function getPreferences() {
        if (!$this->user_id) {
            return [];
        }

        $query = "SELECT * FROM autobooks_notification_preferences
                 WHERE user_id = :user_id";

        $params = [':user_id' => $this->user_id];

        $result = tep_db_query($query, null, $params);

        $preferences = [];
        while ($row = tep_db_fetch_array($result)) {
            $preferences[$row['type']] = (bool)$row['enabled'];
        }

        return $preferences;
    }

    /**
     * Update notification preferences for the current user
     *
     * @param string $type Notification type
     * @param bool $enabled Whether notifications of this type are enabled
     * @return bool Whether the operation was successful
     */
    public function updatePreference($type, $enabled) {
        if (!$this->user_id) {
            return false;
        }

        $query = "INSERT INTO autobooks_notification_preferences
                 (user_id, type, enabled)
                 VALUES (:user_id, :type, :enabled)
                 ON DUPLICATE KEY UPDATE enabled = :enabled";

        $params = [
            ':user_id' => $this->user_id,
            ':type' => $type,
            ':enabled' => $enabled ? 1 : 0
        ];

        $result = tep_db_query($query, null, $params);

        return $result !== false;
    }

    /**
     * Get notifications for the current user
     *
     * @param array $p Request parameters
     * @return string HTML for the notifications list
     */
    public static function get_notifications($p)
    {
        $notifications_manager = new notifications();
        $unread_only = isset($p['unread_only']) && $p['unread_only'] === 'true';
        $limit = isset($p['limit']) ? (int)$p['limit'] : 10;
        $offset = isset($p['offset']) ? (int)$p['offset'] : 0;

        $notification_list = $notifications_manager->getForCurrentUser($unread_only, $limit, $offset);
        $unread_count = $notifications_manager->getUnreadCount();

        // Render the notifications list
        ob_start();
        include FS_VIEWS . '/notifications/notification-list.php';
        return ob_get_clean();
    }
}
