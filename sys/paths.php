<?php
use system\database;

/**
 * Build application paths based on schema
 * 
 * @param array $path Initial path data
 * @return array Complete path data
 */
function build_paths(array $path = []): array {
    // Initialize base paths
    if (!isset($path['fs_app_root'])) {
        $path['fs_app_root'] = str_replace("system", "", __DIR__);
    }

    // Ensure the path ends with a slash for proper concatenation
    if (!str_ends_with($path['fs_app_root'], '/')) {
        $path['fs_app_root'] .= '/';
    }

    // Load path utilities first (needed for fs_path and normalize_path functions)
    require_once $path['fs_app_root'] . 'sys/functions/path_utils.php';

    // Now we can safely normalize the path, preserving leading slash for absolute paths
    $is_absolute = str_starts_with($path['fs_app_root'], '/');
    $normalized = normalize_path($path['fs_app_root']);
    $path['fs_app_root'] = ($is_absolute ? '/' : '') . $normalized . '/';

    // Load request information
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? "https://" : "http://";
    $path['request_uri'] = $_SERVER['REQUEST_URI'] ?? '';
    $path['domain'] = $_SERVER['SERVER_NAME'] ?? '';
    $path['script_name'] = $_SERVER['SCRIPT_NAME'];

    // Clean request URI
    $has_params = strpos($path['request_uri'], '?');
    $path['request_uri'] = $has_params ? substr($_SERVER['REQUEST_URI'], 0, $has_params) : $_SERVER['REQUEST_URI'] ?? '';

    // Set document root paths
    if (!isset($path['fs_doc_root'])) $path['fs_doc_root'] = fs_path($_SERVER['DOCUMENT_ROOT']);
    if (!isset($path['doc_root'])) {
        $doc_root = fs_path($_SERVER['DOCUMENT_ROOT']);
        // Ensure doc_root has trailing slash for proper path concatenation
        $path['doc_root'] = rtrim($doc_root, '/') . '/';
    }

    $path['fs_app'] = realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/';


    
    // Process resource paths
    foreach ($schema['resources'] as $dir => $location) {
        $path['fs_' . $dir] = join_paths($path['fs_app_root'], $location) . '/';
    }
    
    // Process system paths
    foreach ($schema['system'] as $dir => $location) {
        $key = $dir === 'root' ? 'fs_system' : 'fs_sys_' . $dir;
        $path[$key] = join_paths($path['fs_app_root'], $location);
        
        // Add trailing slash for directories
        if ($dir !== 'db_class') {
            $path[$key] .= '/';
        }
    }
    
    // Set external paths
    foreach ($schema['external'] as $key => $location) {
        $path['fs_' . $key] = $location;
    }
    
    // Set app paths
    // Normalize both paths to use forward slashes for consistent comparison
    $normalized_doc_root = str_replace('\\', '/', $path['doc_root']);
    $normalized_fs_app_root = str_replace('\\', '/', $path['fs_app_root']);

    $path['app_root'] = web_path(str_replace($normalized_doc_root, '', $normalized_fs_app_root));
    $path['app_path'] = normalize_path(str_replace($path['app_root'], '', $path['request_uri']));
    $path['path_parts'] = explode('/', $path['app_path']);
    $path['top_level'] = $path['path_parts'][0] ?? '';
    $path['fs_app_path'] = fs_path(join_paths($path['top_level'] === 'system' ? $path['fs_sys_views'] : $path['fs_views'], $path['app_path']));
    $path['app_path'] = str_replace('get_view/', '/', $path['app_path']);
    
    // Set current page
    if (count($path['path_parts']) > 1) {
        $last_element = count($path['path_parts']) - 1;
        $path['current_page'] = $path['path_parts'][$last_element];
    } else {
        $path['current_page'] = $path['top_level'];
    }
    $path['current_page'] = explode('?', $path['current_page'])[0];
    
    // Handle HTMX source paths
    $path['set_by'] = 'default';
    $path['source_path'] = '';
    $path['source_page'] = '';
    $path['source_app_path'] = '';
    
    if (isset($_SERVER['HTTP_HX_CURRENT_URL'])) {
        $path['set_by'] = 'HTTP_HX_CURRENT_URL';
        $path['hx_current_url'] = $_SERVER['HTTP_HX_CURRENT_URL'];
        $path['hx_current_url_parts'] = parse_url($_SERVER['HTTP_HX_CURRENT_URL']);
        $path['source_path_parts'] = explode('/', normalize_path($path['hx_current_url_parts']['path']));
        $path['source_page'] = array_pop($path['source_path_parts']);
        $path['source_path'] = web_path(implode('/', $path['source_path_parts']));
        $path['source_app_path'] = normalize_path(str_replace($path['app_root'], '', $path['source_path']));
        $path['source_fs_path'] = fs_path(join_paths($path['fs_app_root'], 'resources/views', $path['source_app_path']));
    }
    
    // Set database class path and load it
    $path['fs_sys_db_class'] = fs_path(join_paths($path['fs_system'], 'classes/database.class.php'));
    require_once $path['fs_sys_db_class'];
    
    // Set action parameter if not set
    global $input_params;
    if (!isset($input_params['action'])) {
        $temp = explode('/', $path['request_uri']);
        $temp2 = array_pop($temp);
        $input_params['action'] = $temp2;
    }
    
    return $path;
}

/**
 * Define constants from path array
 * 
 * @param array $path Path data
 * @return void
 */
function build_constants($path): void {
    foreach ($path as $key => $value) {
        if (defined(strtoupper($key))) continue;
        if (is_string($value)) {
            // Preserve absolute paths and trailing slashes when normalizing
            $is_absolute = str_starts_with($value, '/');
            $has_trailing_slash = str_ends_with($value, '/');
            $normalized = normalize_path($value);

            // Reconstruct the path preserving leading and trailing slashes
            $value = ($is_absolute ? '/' : '') . $normalized . ($has_trailing_slash ? '/' : '');
        }
        define(strtoupper($key), $value);
    }
}

/**
 * Build routes from database
 * 
 * @param array $path Path data
 * @return array Route data
 */
function build_routes($path): array {
    $route_tree = [];
    $route_list = [];
    if (!defined('API_RUN') || !API_RUN) {
        $routes = database::table('autobooks_navigation as nav')
            ->select(['id', 'parent_path', 'route_key', 'name', 'icon', 'required_roles', 'show_navbar'])
            ->cast([
                'required_roles' => 'array',
                'show_navbar' => 'bool'
            ])
            ->get();
        foreach ($routes as $key => $route) {
            if (in_array($route, ['icon', 'name', 'sub_folder'])) continue;
            $route_list[$route['route_key']] = $route;
            // Process parent path to build tree
            if (empty($route['parent_path'])) {
                // Top-level route
                $route_tree[$route['route_key']] = [
                    'name' => $route['name'],
                    'icon' => $route['icon'],
                    'required_roles' => $route['required_roles'],
                    'show_navbar' => $route['show_navbar']
                ];
            } else {
                // Nested route - find the correct parent
                $parent_path = normalize_path($route['parent_path']);
                $path_parts = array_filter(explode('/', $parent_path));

                // Navigate to the correct position in the tree
                $current = &$route_tree;
                foreach ($path_parts as $part) {
                    if (!isset($current[$part]['sub_folder'])) {
                        $current[$part]['sub_folder'] = [];
                    }
                    $current = &$current[$part]['sub_folder'];
                }

                // Add the route to its parent's sub_folder
                $current[$route['route_key']] = [
                    'name' => $route['name'],
                    'icon' => $route['icon'],
                    'required_roles' => $route['required_roles'],
                    'show_navbar' => $route['show_navbar']
                ];
            }
        };

        define('ROUTE_TREE', $route_tree);
        define('ROUTE_LIST', $route_list);
        define('ROUTES', $routes);
    }
    return $routes ?? [];
}

/**
 * Remove keywords from path
 * 
 * @param string $path Path to process
 * @return string Processed path
 */
function tcs_remove_keywords($path): string {
    return str_replace('get_view/', '/', $path);
}

/**
 * Legacy path normalization function
 * 
 * @param string $path Path to normalize
 * @return string Normalized path
 */
function tcs_path($path): string {
    return normalize_path($path);
}

/**
 * Build API path
 * 
 * @param string $path API endpoint
 * @return string Full API path
 */
function tcs_api($path): string {
    return '/' . APP_ROOT . 'api/' . $path;
}
?>