<?php
/**
 * Path schema configuration
 * Defines the structure of application paths
 */
return [
    // Base directories
    'base' => [
        'app_root' => '{fs_app_root}',
        'doc_root' => '{fs_doc_root}',
    ],
    
    // Resource directories
    'resources' => [
        'classes' => 'resources/classes',
        'functions' => 'resources/functions',
        'views' => 'resources/views',
        'config' => 'resources/config',
        'templates' => 'resources/templates',
        'components' => 'resources/components',
        'uploads' => 'resources/uploads',
        'api' => 'api',
        'logs' => 'logs'
    ],
    
    // System directories
    'system' => [
        'root' => 'sys',
        'classes' => 'sys/classes',
        'functions' => 'sys/functions',
        'views' => 'sys/views',
        'config' => 'sys/config',
        'templates' => 'sys/templates',
        'components' => 'sys/components',
        'logs' => 'sys/logs'
    ],
    
    // External paths
    'external' => [
        'cache' => '/var/www/vhosts/cadservices.co.uk/temp/autobooks',
        'temp' => '/var/www/vhosts/cadservices.co.uk/temp/autobooks'
    ]
];