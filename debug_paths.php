<?php
/**
 * Debug script to understand path calculation issues
 */

// Simulate the environment
$_SERVER['REQUEST_URI'] = '/system';
$_SERVER['SERVER_NAME'] = 'localhost';
$_SERVER['SCRIPT_NAME'] = '/baffletrain/autocadlt/autobooks/index.php';
$_SERVER['SCRIPT_FILENAME'] = 'E:/build/httpdocs/baffletrain/autocadlt/autobooks/index.php';
$_SERVER['DOCUMENT_ROOT'] = 'E:/build/httpdocs';

echo "=== DEBUG PATH CALCULATIONS ===\n";
echo "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "\n";
echo "SCRIPT_NAME: " . $_SERVER['SCRIPT_NAME'] . "\n";
echo "SCRIPT_FILENAME: " . $_SERVER['SCRIPT_FILENAME'] . "\n";
echo "DOCUMENT_ROOT: " . $_SERVER['DOCUMENT_ROOT'] . "\n\n";

// Initialize path array
$path = [];
$path['fs_app_root'] = str_replace("system", "", __DIR__);

echo "Initial fs_app_root: " . $path['fs_app_root'] . "\n";

// Ensure the path ends with a slash for proper concatenation
if (!str_ends_with($path['fs_app_root'], '/')) {
    $path['fs_app_root'] .= '/';
}

echo "fs_app_root with slash: " . $path['fs_app_root'] . "\n";

// Load path utilities
require_once $path['fs_app_root'] . 'system/functions/path_utils.php';

// Normalize the path
$is_absolute = str_starts_with($path['fs_app_root'], '/');
$normalized = normalize_path($path['fs_app_root']);
$path['fs_app_root'] = ($is_absolute ? '/' : '') . $normalized . '/';

echo "Normalized fs_app_root: " . $path['fs_app_root'] . "\n";

// Load request information
$path['request_uri'] = $_SERVER['REQUEST_URI'] ?? '';
$path['domain'] = $_SERVER['SERVER_NAME'] ?? '';
$path['script_name'] = $_SERVER['SCRIPT_NAME'];

echo "request_uri: " . $path['request_uri'] . "\n";
echo "script_name: " . $path['script_name'] . "\n";

// Clean request URI
$has_params = strpos($path['request_uri'], '?');
$path['request_uri'] = $has_params ? substr($_SERVER['REQUEST_URI'], 0, $has_params) : $_SERVER['REQUEST_URI'] ?? '';

echo "Cleaned request_uri: " . $path['request_uri'] . "\n";

// Set document root paths
$path['fs_doc_root'] = fs_path($_SERVER['DOCUMENT_ROOT']);
$doc_root = fs_path($_SERVER['DOCUMENT_ROOT']);
$path['doc_root'] = rtrim($doc_root, '/') . '/';

echo "fs_doc_root: " . $path['fs_doc_root'] . "\n";
echo "doc_root: " . $path['doc_root'] . "\n";

$path['fs_app'] = realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/';
echo "fs_app: " . $path['fs_app'] . "\n";

// Calculate app_root and app_path
// Normalize both paths to use forward slashes for consistent comparison
$normalized_doc_root = str_replace('\\', '/', $path['doc_root']);
$normalized_fs_app_root = str_replace('\\', '/', $path['fs_app_root']);

echo "normalized_doc_root: " . $normalized_doc_root . "\n";
echo "normalized_fs_app_root: " . $normalized_fs_app_root . "\n";

$path['app_root'] = web_path(str_replace($normalized_doc_root, '', $normalized_fs_app_root));
echo "app_root calculation: web_path(str_replace('" . $normalized_doc_root . "', '', '" . $normalized_fs_app_root . "'))\n";
echo "app_root: " . $path['app_root'] . "\n";

$path['app_path'] = normalize_path(str_replace($path['app_root'], '', $path['request_uri']));
echo "app_path calculation: normalize_path(str_replace('" . $path['app_root'] . "', '', '" . $path['request_uri'] . "'))\n";
echo "app_path: " . $path['app_path'] . "\n";

$path['path_parts'] = explode('/', $path['app_path']);
$path['top_level'] = $path['path_parts'][0] ?? '';

echo "path_parts: " . print_r($path['path_parts'], true);
echo "top_level: " . $path['top_level'] . "\n";

// Set current page
if (count($path['path_parts']) > 1) {
    $last_element = count($path['path_parts']) - 1;
    $path['current_page'] = $path['path_parts'][$last_element];
} else {
    $path['current_page'] = $path['top_level'];
}

echo "current_page: " . $path['current_page'] . "\n";

echo "\n=== EXPECTED vs ACTUAL ===\n";
echo "Expected app_path for /system: 'system'\n";
echo "Actual app_path: '" . $path['app_path'] . "'\n";
echo "Expected top_level: 'system'\n";
echo "Actual top_level: '" . $path['top_level'] . "'\n";
?>
